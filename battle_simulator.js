const fs = require('fs');
const path = require('path');

// 导入配置和单位数据
const config = require('./config.js');
const unitData = require('../../~out/design/~selftest/unit.js');
const TraitSystem = require('./trait_system.js');

class Unit {
  constructor(unitConfig, team, id) {
    this.id = id;
    this.type = unitConfig.Id;
    this.name = unitConfig.Name;
    this.team = team;
    
    // 基础属性
    this.maxHp = this.applyRandomVariation(unitConfig.Hp, config.units.randomHpVariation, 0.1);
    this.hp = this.maxHp;
    this.atk = this.applyRandomVariation(unitConfig.Atk, config.units.randomAtkVariation, 0.05);
    this.atkRange = unitConfig.AtkRange;
    this.atkSpeed = unitConfig.AtkSpeed;
    this.speed = unitConfig.Speed;
    this.crit = unitConfig.Crit || config.units.defaultCrit;
    this.critDamage = unitConfig.CritDamage || config.units.defaultCritDamage;
    this.dodge = unitConfig.Dodge || config.units.defaultDodge;
    this.isRangedUnit = unitConfig.IsRangedUnit || 0;
    this.bulletSpeed = unitConfig.BulletSpeed || 0;
    
    // 位置信息
    this.x = 0;
    this.y = 0;
    
    // 战斗状态
    this.alive = true;
    this.target = null;
    this.lastAttackTime = 0;
    this.isMoving = false;
    
    // 统计信息
    this.stats = {
      damageDealt: 0,
      damageTaken: 0,
      kills: 0,
      shots: 0,
      hits: 0,
      crits: 0,
      survivalTime: 0
    };
    
    // 特殊技能
    this.trait = unitConfig.Trait || {};
  }
  
  applyRandomVariation(value, enabled, variation) {
    if (!enabled) return value;
    const factor = 1 + (Math.random() - 0.5) * 2 * variation;
    return Math.round(value * factor);
  }
  
  distanceTo(other) {
    return Math.sqrt((this.x - other.x) ** 2 + (this.y - other.y) ** 2);
  }
  
  canAttack(target, currentTime) {
    if (!this.alive || !target || !target.alive) return false;
    if (currentTime - this.lastAttackTime < 1 / this.atkSpeed) return false;
    return this.distanceTo(target) <= this.atkRange;
  }
  
  attack(target, currentTime, allEnemies) {
    if (!this.canAttack(target, currentTime)) return false;
    
    this.lastAttackTime = currentTime;
    this.stats.shots++;
    
    // 计算命中
    const hitChance = config.units.baseHitRate - target.dodge;
    const hit = Math.random() < hitChance;
    
    if (!hit) {
      if (config.logging.verbose) {
        console.log(`${this.name} 攻击 ${target.name} 但是MISS了！`);
      }
      return false;
    }
    
    this.stats.hits++;
    
    // 计算基础伤害
    let damage = this.atk;
    
    // 暴击判定
    const isCrit = Math.random() < this.crit;
    if (isCrit) {
      damage *= this.critDamage;
      this.stats.crits++;
    }
    
    // 处理特殊技能
    if (TraitSystem.hasTrait(this, 'UnitRingAtk')) {
      // 旋转攻击
      const range = TraitSystem.getTraitParam(this, 'UnitRingAtk');
      const affectedTargets = TraitSystem.handleRingAttack(this, allEnemies, damage, range);
      
      if (config.logging.verbose) {
        console.log(`${this.name} 使用旋转攻击，影响了 ${affectedTargets.length} 个敌人！`);
      }
      
    } else if (TraitSystem.hasTrait(this, 'UnitAoeAtkExplosion')) {
      // 远程爆炸攻击
      const [range, damageMultiplier] = TraitSystem.getTraitParam(this, 'UnitAoeAtkExplosion');
      const affectedTargets = TraitSystem.handleExplosionAttack(
        this, target, allEnemies, damage, range, damageMultiplier
      );
      
      if (config.logging.verbose) {
        console.log(`${this.name} 的攻击发生爆炸，影响了 ${affectedTargets.length} 个敌人！`);
      }
      
    } else {
      // 普通单体攻击
      // 应用护盾减伤
      if (TraitSystem.hasTrait(target, 'UnitShieldBuff')) {
        const shieldPercent = TraitSystem.getTraitParam(target, 'UnitShieldBuff');
        damage = TraitSystem.applyShieldReduction(damage, shieldPercent);
      }
      
      damage = Math.round(damage);
      target.takeDamage(damage);
      this.stats.damageDealt += damage;
      
      if (config.logging.verbose) {
        console.log(`${this.name} 对 ${target.name} 造成了 ${damage} 点伤害${isCrit ? '（暴击）' : ''}`);
      }
      
      if (!target.alive) {
        this.stats.kills++;
      }
    }
    
    return true;
  }
  
  takeDamage(damage) {
    this.hp -= damage;
    this.stats.damageTaken += damage;
    
    if (this.hp <= 0) {
      this.hp = 0;
      this.alive = false;
    }
  }
  
  findTarget(enemies) {
    if (!this.alive) return null;
    
    const validTargets = enemies.filter(enemy => 
      enemy.alive && this.distanceTo(enemy) <= this.atkRange
    );
    
    if (validTargets.length === 0) return null;
    
    switch (config.ai.targetStrategy) {
      case 'nearest':
        return validTargets.reduce((closest, enemy) => 
          this.distanceTo(enemy) < this.distanceTo(closest) ? enemy : closest
        );
      case 'weakest':
        return validTargets.reduce((weakest, enemy) => 
          enemy.hp < weakest.hp ? enemy : weakest
        );
      case 'strongest':
        return validTargets.reduce((strongest, enemy) => 
          enemy.hp > strongest.hp ? enemy : strongest
        );
      case 'random':
        return validTargets[Math.floor(Math.random() * validTargets.length)];
      default:
        return validTargets[0];
    }
  }
  
  move(enemies, currentTime) {
    if (!this.alive) return;
    
    // 如果有目标且在射程内，不移动
    if (this.target && this.target.alive && this.distanceTo(this.target) <= this.atkRange) {
      this.isMoving = false;
      return;
    }
    
    // 寻找最近的敌人
    const nearestEnemy = enemies.reduce((nearest, enemy) => {
      if (!enemy.alive) return nearest;
      if (!nearest) return enemy;
      return this.distanceTo(enemy) < this.distanceTo(nearest) ? enemy : nearest;
    }, null);
    
    if (!nearestEnemy) return;
    
    // 计算移动方向
    const dx = nearestEnemy.x - this.x;
    const dy = nearestEnemy.y - this.y;
    const distance = Math.sqrt(dx * dx + dy * dy);
    
    if (distance > this.atkRange) {
      // 向目标移动
      const moveDistance = this.speed * config.battle.timeStep;
      this.x += (dx / distance) * moveDistance;
      this.y += (dy / distance) * moveDistance;
      this.isMoving = true;
    } else {
      this.isMoving = false;
    }
  }
  
  update(enemies, currentTime) {
    if (!this.alive) return;
    
    this.stats.survivalTime = currentTime;
    this.move(enemies, currentTime);
    this.target = this.findTarget(enemies);
    
    if (this.target) {
      this.attack(this.target, currentTime, enemies);
    }
  }
}

class BattleSimulator {
  constructor() {
    this.battleResults = [];
    this.unitStats = {};
  }
  
  getRandomUnits(count) {
    const unitTypes = Object.keys(unitData);
    const selectedUnits = [];
    
    for (let i = 0; i < count; i++) {
      const randomType = unitTypes[Math.floor(Math.random() * unitTypes.length)];
      selectedUnits.push(unitData[randomType]);
    }
    
    return selectedUnits;
  }
  
  createTeam(teamId, unitConfigs) {
    const team = [];
    const startX = teamId === 0 ? 5 : config.battle.mapSize.width - 5;
    
    unitConfigs.forEach((unitConfig, index) => {
      const unit = new Unit(unitConfig, teamId, `${teamId}_${index}`);
      unit.x = startX + (Math.random() - 0.5) * 10;
      unit.y = 5 + index * 4 + (Math.random() - 0.5) * 2;
      team.push(unit);
    });
    
    return team;
  }
  
  simulateBattle(battleId) {
    if (config.logging.verbose) {
      console.log(`\\n=== 战斗 ${battleId + 1} 开始 ===`);
    }
    
    // 创建两支队伍
    const team1Units = this.getRandomUnits(config.battle.unitsPerTeam);
    const team2Units = this.getRandomUnits(config.battle.unitsPerTeam);
    
    const team1 = this.createTeam(0, team1Units);
    const team2 = this.createTeam(1, team2Units);
    
    let currentTime = 0;
    let round = 0;
    
    // 战斗循环
    while (round < config.battle.maxRounds) {
      // 检查胜负
      const team1Alive = team1.filter(unit => unit.alive);
      const team2Alive = team2.filter(unit => unit.alive);
      
      if (team1Alive.length === 0 || team2Alive.length === 0) {
        break;
      }
      
      // 更新所有单位
      team1.forEach(unit => unit.update(team2, currentTime));
      team2.forEach(unit => unit.update(team1, currentTime));
      
      currentTime += config.battle.timeStep;
      round++;
    }
    
    // 判定胜负
    const team1Survivors = team1.filter(unit => unit.alive).length;
    const team2Survivors = team2.filter(unit => unit.alive).length;
    
    let winner;
    if (team1Survivors > team2Survivors) {
      winner = 1;
    } else if (team2Survivors > team1Survivors) {
      winner = 2;
    } else {
      winner = 0; // 平局
    }
    
    const battleResult = {
      battleId: battleId + 1,
      winner,
      team1Survivors,
      team2Survivors,
      rounds: round,
      duration: currentTime,
      team1Units: team1Units.map(u => u.Name),
      team2Units: team2Units.map(u => u.Name)
    };
    
    if (config.logging.showBattleResults) {
      console.log(`战斗 ${battleId + 1}: 队伍${winner === 0 ? '平局' : winner + '获胜'} (${team1Survivors} vs ${team2Survivors}) 用时${round}回合`);
    }
    
    // 收集单位统计
    this.collectUnitStats([...team1, ...team2]);
    
    return battleResult;
  }
  
  collectUnitStats(units) {
    units.forEach(unit => {
      const key = unit.type;
      if (!this.unitStats[key]) {
        this.unitStats[key] = {
          name: unit.name,
          battles: 0,
          wins: 0,
          totalDamage: 0,
          totalDamageTaken: 0,
          totalKills: 0,
          totalSurvivalTime: 0,
          totalShots: 0,
          totalHits: 0,
          totalCrits: 0
        };
      }
      
      const stats = this.unitStats[key];
      stats.battles++;
      if (unit.alive) stats.wins++;
      stats.totalDamage += unit.stats.damageDealt;
      stats.totalDamageTaken += unit.stats.damageTaken;
      stats.totalKills += unit.stats.kills;
      stats.totalSurvivalTime += unit.stats.survivalTime;
      stats.totalShots += unit.stats.shots;
      stats.totalHits += unit.stats.hits;
      stats.totalCrits += unit.stats.crits;
    });
  }
  
  runSimulation() {
    console.log(`开始作战模拟 - 总共 ${config.battle.totalBattles} 场战斗`);
    console.log(`每队 ${config.battle.unitsPerTeam} 个单位`);
    console.log('=' * 50);
    
    let team1Wins = 0;
    let team2Wins = 0;
    let draws = 0;
    
    for (let i = 0; i < config.battle.totalBattles; i++) {
      const result = this.simulateBattle(i);
      this.battleResults.push(result);
      
      if (result.winner === 1) {
        team1Wins++;
      } else if (result.winner === 2) {
        team2Wins++;
      } else {
        draws++;
      }
    }
    
    // 显示最终统计
    if (config.logging.showFinalStats) {
      this.displayFinalStats(team1Wins, team2Wins, draws);
    }
    
    // 保存战斗日志
    if (config.logging.saveBattleLog) {
      this.saveBattleLog();
    }
  }
  
  displayFinalStats(team1Wins, team2Wins, draws) {
    console.log('\\n' + '=' * 50);
    console.log('最终统计结果');
    console.log('=' * 50);
    
    console.log(`总战斗场次: ${config.battle.totalBattles}`);
    console.log(`队伍1胜利: ${team1Wins} (${(team1Wins/config.battle.totalBattles*100).toFixed(1)}%)`);
    console.log(`队伍2胜利: ${team2Wins} (${(team2Wins/config.battle.totalBattles*100).toFixed(1)}%)`);
    console.log(`平局: ${draws} (${(draws/config.battle.totalBattles*100).toFixed(1)}%)`);
    
    if (config.statistics.trackUnitPerformance) {
      console.log('\\n单位表现统计:');
      console.log('-' * 80);
      console.log('单位名称    参战次数    胜率    平均伤害    平均击杀    命中率    暴击率');
      console.log('-' * 80);
      
      Object.entries(this.unitStats)
        .sort((a, b) => b[1].wins / b[1].battles - a[1].wins / a[1].battles)
        .forEach(([type, stats]) => {
          const winRate = (stats.wins / stats.battles * 100).toFixed(1);
          const avgDamage = (stats.totalDamage / stats.battles).toFixed(0);
          const avgKills = (stats.totalKills / stats.battles).toFixed(1);
          const hitRate = stats.totalShots > 0 ? (stats.totalHits / stats.totalShots * 100).toFixed(1) : '0.0';
          const critRate = stats.totalHits > 0 ? (stats.totalCrits / stats.totalHits * 100).toFixed(1) : '0.0';
          
          console.log(`${stats.name}    ${stats.battles}    ${winRate}%    ${avgDamage}    ${avgKills}    ${hitRate}%    ${critRate}%`);
        });
    }
  }
  
  saveBattleLog() {
    const logContent = JSON.stringify({
      config: config,
      battleResults: this.battleResults,
      unitStats: this.unitStats
    }, null, 2);
    
    fs.writeFileSync(config.logging.logFilePath, logContent);
    console.log(`\\n战斗日志已保存到: ${config.logging.logFilePath}`);
  }
}

// 主函数
function main() {
  const simulator = new BattleSimulator();
  simulator.runSimulation();
}

// 如果直接运行此文件，则启动模拟
if (require.main === module) {
  main();
}

module.exports = { BattleSimulator, Unit };


