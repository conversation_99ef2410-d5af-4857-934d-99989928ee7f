var g_table;
var g_tblInfo;
(function(g_table,g_tblInfo){

var tname = "unit";
var data = {
  "Infantry": {
    "Id": "Infantry",
    "&Name": "5q2l5YW1",
    "Body": "infantry",
    "Atk": 30,
    "Hp": 300,
    "AtkRange": 2.5,
    "AtkSpeed": 1.2,
    "Speed": 1.92,
    "Crit": 0.05,
    "CritDamage": 2,
    "Dodge": 0.05,
    "IsRangedUnit": 0,
    "Quality": 3,
    "Exclusive": 50,
    "&Desc": "d830283a8d41f72d3ae20243c3053cae",
    "Name": "步兵",
    "Desc": "手持长剑的精锐战士，组成军队的中坚力量。"
  },
  "Berserker": {
    "Id": "Berserker",
    "&Name": "5pan5YW1",
    "Body": "Axeman",
    "Trait": {
      "UnitRingAtk": 2.5
    },
    "Atk": 40,
    "Hp": 350,
    "AtkRange": 2.5,
    "AtkSpeed": 0.9,
    "Speed": 1.7600000000000002,
    "Crit": 0.05,
    "CritDamage": 2,
    "Dodge": 0.05,
    "IsRangedUnit": 0,
    "Quality": 5,
    "Exclusive": 51,
    "WeaponUnlockCond": 13,
    "&Desc": "86ca34e4c2946c7c8d7ea669f3a21500",
    "Name": "斧兵",
    "Desc": "来自北方蛮族的狂暴战士，挥舞双手战斧，尤其擅长应对敌人的围攻。"
  },
  "Tank": {
    "Id": "Tank",
    "&Name": "55u+5YW1",
    "Body": "Shield-Soldier",
    "Trait": {
      "UnitShieldBuff": 0.3
    },
    "Atk": 14,
    "Hp": 400,
    "AtkRange": 2.5,
    "AtkSpeed": 0.8,
    "Speed": 1.4400000000000002,
    "Crit": 0.05,
    "CritDamage": 2,
    "Dodge": 0.05,
    "IsRangedUnit": 0,
    "Quality": 4,
    "Exclusive": 52,
    "WeaponUnlockCond": 4,
    "&Desc": "29f9851da5ece24fead9ee9c67e76b48",
    "Name": "盾兵",
    "Desc": "纪律严明的重装防御者，手持巨盾，组成密不透风的盾墙。"
  },
  "Archer": {
    "Id": "Archer",
    "&Name": "5byT5YW1",
    "Body": "Archer",
    "Atk": 20,
    "Hp": 75,
    "AtkRange": 10,
    "AtkSpeed": 1,
    "Speed": 1.7600000000000002,
    "Crit": 0.05,
    "CritDamage": 2,
    "Dodge": 0.05,
    "BulletSpeed": 18,
    "IsRangedUnit": 1,
    "Quality": 3,
    "Exclusive": 53,
    "WeaponUnlockCond": 2,
    "&Desc": "c01519b0a6d00ff95829cef52ee220fe",
    "Name": "弓兵",
    "Desc": "训练有素的远程射手，能压制近战部队，但近身时几乎毫无还手之力。"
  },
  "Peltast": {
    "Id": "Peltast",
    "&Name": "5oqV55+b5YW1",
    "Body": "Lancer",
    "Atk": 35,
    "Hp": 75,
    "AtkRange": 9,
    "AtkSpeed": 0.7,
    "Speed": 1.4400000000000002,
    "Crit": 0.05,
    "CritDamage": 2,
    "Dodge": 0.05,
    "BulletSpeed": 15,
    "IsRangedUnit": 1,
    "Quality": 4,
    "Exclusive": 54,
    "WeaponUnlockCond": 8,
    "&Desc": "0285eb76b2f0f9c48e4c019daaac2b16",
    "Name": "投矛兵",
    "Desc": "轻装散兵，携带短矛，在接敌前投掷以削弱敌方攻势。"
  },
  "Mage": {
    "Id": "Mage",
    "&Name": "5rOV5biI",
    "Body": "Magician",
    "Trait": {
      "UnitAoeAtkExplosion": [
        2,
        0.7
      ]
    },
    "Atk": 55,
    "Hp": 75,
    "AtkRange": 8,
    "AtkSpeed": 0.5,
    "Speed": 1.7600000000000002,
    "Crit": 0.05,
    "CritDamage": 2,
    "Dodge": 0.05,
    "BulletSpeed": 19.5,
    "IsRangedUnit": 1,
    "Quality": 5,
    "Exclusive": 55,
    "WeaponUnlockCond": 17,
    "&Desc": "60ee48b84448b1ffe9a6ba3621324945",
    "Name": "法师",
    "Desc": "神秘的学者，掌握火焰之术。能在战场释放毁灭性魔法，但施法缓慢。"
  }
};
var tm = "254793_2211";

if(typeof(module)=='object' && typeof(laya)=='undefined'){
  module.exports = data;
  global.CUR_TMARK = tm;
}else{
  g_table[tname] = data;
  g_tblInfo[tname] = tm;
} 
})( g_table||(g_table={}) , g_tblInfo||(g_tblInfo={})  );